<template>
    <el-card shadow="never" class="!border-none">
        <el-page-header :content="pageTitle" @back="$router.back()" />
    </el-card>

    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="pl-[80px] pr-[100px]">
            <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="mt-6">
                    <el-form-item label="标题" prop="title">
                        <el-input
                            v-model="formData.title"
                            placeholder="请输入无人机标题"
                            maxlength="100"
                            show-word-limit
                            class="w-80"
                        />
                    </el-form-item>

                    <el-form-item label="品牌" prop="brand">
                        <el-input
                            v-model="formData.brand"
                            placeholder="请输入品牌名称"
                            maxlength="50"
                            show-word-limit
                            class="w-80"
                        />
                    </el-form-item>

                    <el-form-item label="押金" prop="deposit">
                        <el-input-number
                            v-model="formData.deposit"
                            :min="0"
                            :precision="2"
                            placeholder="请输入押金金额"
                            class="w-80"
                        />
                    </el-form-item>

                    <el-form-item label="租用单价" prop="price">
                        <el-input-number
                            v-model="formData.price"
                            :min="0"
                            :precision="2"
                            placeholder="请输入租用单价"
                            class="w-80"
                        />
                    </el-form-item>

                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="formData.status">
                            <el-radio :label="1">上架</el-radio>
                            <el-radio :label="0">下架</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="排序" prop="sort">
                        <el-input-number
                            v-model="formData.sort"
                            :min="0"
                            placeholder="请输入排序值"
                            class="w-80"
                        />
                    </el-form-item>

                    <el-form-item label="无人机图片" prop="images">
                        <div>
                            <div>
                                <material-picker
                                    v-model="formData.images"
                                    :limit="9"
                                    type="image"
                                />
                            </div>
                            <div class="form-tips">
                                最多可上传9张图片，建议尺寸：750*750像素
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="描述" prop="content">
                        <div class="w-80">
                            <el-input
                                v-model="formData.content"
                                type="textarea"
                                :autosize="{ minRows: 4, maxRows: 8 }"
                                placeholder="请输入无人机描述"
                                maxlength="500"
                                show-word-limit
                            />
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>

        <footer-btns>
            <el-button @click="$router.back()">取消</el-button>
            <el-button type="primary" @click="handleSave">保存</el-button>
        </footer-btns>
</template>

<script lang="ts" setup name="droneEdit">
import type { FormInstance } from 'element-plus'
import { apiDroneEdit, apiDroneDetail } from '@/api/marketing/drone'
import useMultipleTabs from '@/hooks/useMultipleTabs'
import feedback from '@/utils/feedback'

const route = useRoute()
const router = useRouter()

const isEdit = computed(() => !!route.query.id)
const pageTitle = computed(() => isEdit.value ? '编辑无人机' : '添加无人机')

const formData = reactive({
    id: '',
    title: '',
    brand: '',
    deposit: 0,
    price: 0,
    status: 1,
    sort: 0,
    images: [] as string[],
    content: ''
})

const { removeTab } = useMultipleTabs()
const formRef = shallowRef<FormInstance>()

const rules = reactive({
    title: [{ required: true, message: '请输入无人机标题', trigger: 'blur' }],
    brand: [{ required: true, message: '请输入品牌名称', trigger: 'blur' }],
    deposit: [{ required: true, message: '请输入押金金额', trigger: 'blur' }],
    price: [{ required: true, message: '请输入租用单价', trigger: 'blur' }],
    images: [
        {
            required: true,
            message: '请上传无人机图片',
            trigger: 'change',
            validator: (_rule: any, value: any, callback: any) => {
                if (!value || value.length === 0) {
                    callback(new Error('请上传无人机图片'))
                } else {
                    callback()
                }
            }
        }
    ]
})

// 获取详情
const getDetails = async () => {
    const data = await apiDroneDetail({
        id: route.query.id
    })
    Object.keys(formData).forEach((key) => {
        //@ts-ignore
        formData[key] = data[key]
    })
}

// 保存
const handleSave = async () => {
    try {
        await formRef.value?.validate()
        await apiDroneEdit(formData)
        // feedback.msgSuccess(isEdit.value ? '编辑成功' : '添加成功')
        // 先跳转到列表页，再关闭当前标签页
        await router.push('/application/drone/drone')
        removeTab()
    } catch (error) {
        console.error('保存失败:', error)
        // 错误信息会由 API 层自动处理和显示
    }
}

// 如果有ID参数，则获取详情
if (route.query.id) {
    getDetails()
}
</script>

<style lang="scss" scoped>
.form-tips {
    color: var(--el-text-color-regular);
    font-size: 12px;
    margin-top: 8px;
    line-height: 1.5;
}

// 修复图片上传组件的布局问题
:deep(.material-select__trigger) {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;

    .draggable {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
    }

    .material-preview,
    .material-upload {
        float: none !important;
        margin-right: 8px;
        margin-bottom: 8px;
    }
}
</style>
