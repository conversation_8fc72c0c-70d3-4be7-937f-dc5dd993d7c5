<template>
    <div class="menu flex-1 min-h-0" :class="themeClass">
        <el-scrollbar>
            <el-menu
                ref="menuRef"
                v-bind="config"
                :default-active="activeMenu"
                :collapse="isCollapsed"
                mode="vertical"
                :key="`menu-${activeMenu}`"
                @select="$emit('select')"
            >
                <menu-item
                    v-for="route in routes"
                    :key="route.path"
                    :route="route"
                    :route-path="route.path"
                    :popper-class="themeClass"
                />
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import MenuItem from './menu-item.vue'
import type { RouteRecordRaw } from 'vue-router'

const props = defineProps({
    routes: {
        type: Object as PropType<RouteRecordRaw[]>
    },
    config: {
        type: Object
    },
    isCollapsed: {
        type: Boolean,
        default: false
    },
    theme: {
        type: String
    }
})

defineEmits(['select'])

const route = useRoute()
const menuRef = ref()

const activeMenu = computed<string>(() => (route.meta?.activeMenu as string) ?? route.path)
const themeClass = computed(() => `theme-${props.theme}`)

// 监听路由变化，主动更新菜单选中状态
watch(activeMenu, (newActiveMenu) => {
    console.log('菜单activeMenu变化:', {
        newActiveMenu,
        routePath: route.path,
        routeFullPath: route.fullPath,
        routeMeta: route.meta
    })

    if (menuRef.value && newActiveMenu) {
        // 使用nextTick确保DOM已更新
        nextTick(() => {
            console.log('更新菜单选中状态:', newActiveMenu)
            console.log('菜单组件实例:', menuRef.value)

            // 尝试多种方法来更新菜单选中状态
            if (menuRef.value) {
                // 方法1: 直接设置activeIndex
                menuRef.value.activeIndex = newActiveMenu

                // 方法2: 如果有updateActiveIndex方法
                if (typeof menuRef.value.updateActiveIndex === 'function') {
                    menuRef.value.updateActiveIndex(newActiveMenu)
                }

                // 方法3: 如果有handleSelect方法
                if (typeof menuRef.value.handleSelect === 'function') {
                    menuRef.value.handleSelect(newActiveMenu, [], null)
                }

                console.log('菜单当前activeIndex:', menuRef.value.activeIndex)
            }
        })
    }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.menu {
    &.theme-dark {
        .el-menu {
            :deep(.el-menu-item) {
                &.is-active {
                    @apply bg-primary border-primary;
                }
            }
        }
        :deep(.el-menu--collapse) {
            .el-sub-menu.is-active .el-sub-menu__title {
                @apply bg-primary #{!important};
            }
        }
    }
    &.theme-light {
        :deep(.el-menu) {
            .el-menu-item {
                border-color: transparent;
                &.is-active {
                    @apply bg-primary-light-9 border-r-2 border-primary;
                }
            }
            .el-menu-item:hover,
            .el-sub-menu__title:hover {
                color: var(--el-color-primary);
            }
        }
    }
    .el-menu {
        border-right: none;
        &:not(.el-menu--collapse) {
            width: var(--aside-width);
        }
    }
}
</style>
